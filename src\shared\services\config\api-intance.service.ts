import axios, { AxiosError, AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from "axios";
import Cookies from "js-cookie";
import { refreshTokenRequest } from "@/modules/auth/api/refresh.request";
import { LMPStore } from "@/shared/providers/global.provider";
import { authStateAtom } from "@/modules/auth/states/auth.state";
import { userProfileAtom } from "@/modules/auth/states/user.state";

export const API_BASE_URL: string = import.meta.env.VITE_API_BASE_URL;

export const apiInstance: AxiosInstance = axios.create({
	baseURL: "/api",
	withCredentials: true,
	// timeout: 3000,
});

let isRefreshing = false;
let failedQueue: Array<{
	resolve: (value: unknown) => void;
	reject: (reason: unknown) => void;
}> = [];

const processQueue = (error: AxiosError | null, token: string | null = null) => {
	failedQueue.forEach(({ resolve, reject }) => {
		if (error) {
			reject(error);
		} else {
			resolve(token);
		}
	});

	failedQueue = [];
};

const invalidateUserState = () => {
	Cookies.remove("access_token");
	Cookies.remove("refresh_token");
	LMPStore.set(authStateAtom, { isAuthenticated: false });
	LMPStore.set(userProfileAtom, null);
	localStorage.removeItem("authState");
	localStorage.removeItem("userProfile");
};

interface ITokenProvider {
	getToken(): string | undefined;
}

class TokenProvider implements ITokenProvider {
	getToken(): string | undefined {
		return Cookies.get("refresh_token");
	}
}

class ApiInterceptorManager {
	constructor(
		private axiosInstance: AxiosInstance,
		private tokenProvider: ITokenProvider
	) {}

	addRequestInterceptors(): void {
		this.axiosInstance.interceptors.request.use(
			(config: InternalAxiosRequestConfig): InternalAxiosRequestConfig => {
				const token: string | undefined = this.tokenProvider.getToken();
				if (token) {
					config.headers = config.headers || {};
					config.headers.Authorization = `Bearer ${token}`;
				}
				return config;
			},
			(error: AxiosError): Promise<AxiosError> => {
				return Promise.reject(error);
			}
		);
	}

	addResponseInterceptors(): void {
		this.axiosInstance.interceptors.response.use(
			(response: AxiosResponse): AxiosResponse => {
				const contentType = response.headers["content-type"];

				if (contentType && !contentType.includes("application/json") && !contentType.includes("application/pdf")) {
					throw new Error("Formato de resposta inesperado: esperado JSON ou PDF, mas recebido outro tipo.");
				}
				return response;
			},
			async (error: AxiosError): Promise<AxiosResponse | never> => {
				const originalRequest = error.config as InternalAxiosRequestConfig & { _retry?: boolean };

				if (error.response) {
					console.error("Response Error:", error.response);
				}

		
				if (error.response?.status === 401 && !originalRequest._retry && !originalRequest.url?.includes("/auth/refresh")) {
					if (isRefreshing) {
						return new Promise((resolve, reject) => {
							failedQueue.push({ resolve, reject });
						})
							.then(() => {
								return this.axiosInstance(originalRequest);
							})
							.catch(err => {
								return Promise.reject(err);
							});
					}

					originalRequest._retry = true;
					isRefreshing = true;

					const refreshToken = Cookies.get("refresh_token");

					if (!refreshToken) {
						// Não há refresh token, invalida o estado
						invalidateUserState();
						processQueue(error, null);
						isRefreshing = false;
						return Promise.reject(error);
					}

					try {
						// Tenta fazer refresh do token
						const response = await refreshTokenRequest({ refresh_token: refreshToken });

						if (response.success) {
							const { access_token, refresh_token: newRefreshToken } = response.data.data;

							// Atualiza os cookies
							Cookies.set("access_token", access_token);
							Cookies.set("refresh_token", newRefreshToken);

							// Atualiza o header da requisição original
							originalRequest.headers = originalRequest.headers || {};
							originalRequest.headers.Authorization = `Bearer ${access_token}`;

							// Processa a fila de requisições pendentes
							processQueue(null, access_token);
							isRefreshing = false;

							// Reexecuta a requisição original
							return this.axiosInstance(originalRequest);
						} else {
							// Refresh falhou, invalida o estado
							invalidateUserState();
							processQueue(error, null);
							isRefreshing = false;
							return Promise.reject(error);
						}
					} catch (refreshError) {
						// Erro no refresh, invalida o estado
						invalidateUserState();
						processQueue(error, null);
						isRefreshing = false;
						return Promise.reject(refreshError);
					}
				}

				return Promise.reject(error);
			}
		);
	}
}

const tokenProvider = new TokenProvider();
const interceptorManager = new ApiInterceptorManager(apiInstance, tokenProvider);

interceptorManager.addRequestInterceptors();
interceptorManager.addResponseInterceptors();

export default apiInstance;
